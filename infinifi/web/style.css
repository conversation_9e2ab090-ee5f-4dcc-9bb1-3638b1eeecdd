:root {
  font-family: monospace;
  --text: #4c4f69;
  --surface0: #ccd0da;
  --surface1: #bcc0cc;
  --base: #eff1f5;
  --lavender: #7287fd;
  --maroon: #e64553;

  --range-height: 12px;
  --range-accent: var(--lavender);
  --link-accent: var(--lavender);
}

@media (prefers-color-scheme: dark) {
  :root {
    --text: #cdd6f4;
    --surface1: #45475a;
    --base: #1e1e2e;
    --lavender: #b4befe;
    --maroon: #eba0ac;
    --range-accent: var(--maroon);
    --link-accent: var(--maroon);
  }
}

html,
body {
  width: 100%;
}

body {
  width: calc(100% - 4rem - 2px);
  height: calc(100vh - 6rem - 2px);
  height: calc(100svh - 6rem - 2px);
  padding: 2rem;
  margin: 0;
  background-color: var(--base);
  overflow: hidden;
}
@media (min-width: 768px) {
  body {
    width: calc(100% - 8rem - 2px);
    height: calc(100vh - 10rem - 2px);
    height: calc(100svh - 10rem - 2px);
    padding: 4rem;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: 400;
  color: var(--text);
}

main {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin: 2rem;
}
@media (min-width: 768px) {
  main {
    margin: 4rem;
  }
}

footer {
  padding: 1rem 0;
  align-items: center;
  justify-content: center;
  color: var(--text);
  text-align: center;
  text-wrap: nowrap;
  overflow: auto;
}
@media (min-width: 768px) {
  footer {
    padding: 2rem 0;
  }
}

a {
  color: var(--link-accent);
}

#bg {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -10;
}

.container {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
  align-items: end;
  justify-content: start;
  border: 2px solid var(--text);
  border-radius: 2px;
}

.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -10;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 2rem;
  z-index: 0;
  color: var(--text);
}

.status-bar > #listener-count {
  margin: 0;
  opacity: 0.8;
  padding-right: 1rem;
}

.header {
  font-weight: 800;
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  main {
    width: 80%;
  }
}

@media (min-width: 1024px) {
  main {
    width: 50%;
  }
}

.button-container {
  margin-top: 2rem;
  display: flex;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="range"] {
    overflow: hidden;
    height: var(--range-height);
    appearance: none;
    border: 2px solid var(--text);
    background-color: var(--base);
  }

  input[type="range"]::-webkit-slider-runnable-track {
    height: var(--range-height);
    appearance: none;
    color: #13bba4;
  }

  input[type="range"]::-webkit-slider-thumb {
    width: var(--range-height);
    height: var(--range-height);
    appearance: none;
    background: var(--text);
    box-shadow: -80px 0 0 80px var(--range-accent);
  }
}

input[type="range"]::-moz-range-progress {
  border: none;
  height: var(--range-height);
  background-color: var(--range-accent);
}

input[type="range"]::-moz-range-track {
  border: none;
  background-color: var(--base);
}

input[type="range"]::-moz-range-thumb {
  appearance: none;
  border: none;
  border-radius: 0;
  width: var(--range-height);
  height: var(--range-height);
  background-color: var(--text);
}

/* IE*/
input[type="range"]::-ms-fill-lower {
  background-color: var(--range-accent);
}
input[type="range"]::-ms-fill-upper {
  background-color: var(--base);
}

.button {
  border: 2px solid var(--text);
  background: var(--base);
  position: relative;
  font-family: monospace;
  font-weight: 800;
  padding: 0.5rem 1rem;
  color: var(--text);
  width: 68px;
  text-align: center;
}

.button:hover {
  background-color: var(--surface1);
  background-size: 10px 10px;
  background-image: repeating-linear-gradient(
    45deg,
    var(--surface1) 0,
    var(--surface1) 1px,
    var(--base) 0,
    var(--base) 50%
  );
}

@media (prefers-color-scheme: dark) {
  .button:hover {
    background-color: #1e1e2e;
    opacity: 1;
    background-size: 10px 10px;
    background-image: repeating-linear-gradient(
      45deg,
      #585b70 0,
      #585b70 1px,
      #1e1e2e 0,
      #1e1e2e 50%
    );
  }
}

.button.button-active,
.button:active {
  background: var(--text) !important;
  color: var(--base);
  transform: translate(4px, 4px);
}

.button.button-active::after,
.button:active::after {
  content: none;
}

.button:after {
  display: block;
  position: absolute;
  margin: -2px -2px;
  top: 1px;
  left: 1px;
  right: -1px;
  bottom: -1px;
  padding: 0.5rem 1rem;
  content: "";
  background-color: var(--text);
  z-index: -1;
  transform: translate(2px, 2px);
  width: 42px;
}

@media (prefers-color-scheme: dark) {
  .button:after {
    opacity: 0.5;
  }
}

#cat {
  position: absolute;
  bottom: 0px;
  right: 2rem;
  image-rendering: pixelated;
  height: 30px;
}
@media (min-width: 1024px) {
  #cat {
    right: 4rem;
  }
}

#heart {
  display: none;
  position: absolute;
  bottom: 24px;
  right: 2rem;
  image-rendering: pixelated;
}
@media (min-width: 1024px) {
  #heart {
    right: 4rem;
  }
}

@keyframes heart-animation {
  0% {
    display: block;
  }

  50% {
    transform: translate(0, -24px);
  }

  100% {
    opacity: 0;
    transform: translate(0, -30px);
  }
}

#eeping-cat {
  position: absolute;
  image-rendering: pixelated;
  top: -16px;
  right: 50%;
  height: 15px;
}

#volume-slider-container {
  display: none;
  justify-content: start;
  align-items: center;
}

#volume-slider-container > output {
  color: var(--text);
  margin-right: 1rem;
}

aside#notification {
  display: none;
  position: absolute;
  top: 0;
  right: 0;
  background: var(--surface0);
  padding: 1rem 2rem;
  margin: 2rem;
  border: 2px solid var(--text);
}

aside#notification > p {
  margin: 0;
}

aside#notification > #notification-title {
  font-weight: 700;
  margin-bottom: 0.5rem;
}

@keyframes notification-fade-in {
  0% {
    display: block;
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes notification-fade-out {
  0% {
    opactiy: 1;
  }

  100% {
    opacity: 0;
  }
}

.fal-logo {
  vertical-align: bottom;
  height: 1rem;
}

.fal-link {
  text-decoration: none;
}
